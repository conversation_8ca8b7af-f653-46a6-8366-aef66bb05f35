{% extends 'base.html.twig' %}

{% block title %}Spaces{% endblock %}

{% block content %}
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2">
                {% if searchQuery %}
                    Résultats de recherche pour "{{ searchQuery }}"
                {% else %}
                    Espaces disponibles
                {% endif %}
            </h1>
            {% if searchQuery %}
                <div class="flex items-center text-sm text-gray-600 dark:text-gray-400 mb-4 sm:mb-0">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                    </svg>
                    <span>{{ spaces|length }} résultat(s) trouvé(s)</span>
                    <a href="{{ path('app_space_index') }}" class="ml-3 text-blue-600 hover:underline dark:text-blue-400">
                        Effacer la recherche
                    </a>
                </div>
            {% endif %}
        </div>
        {% if is_host() %}
        <a href="{{ path('app_space_new') }}" class="w-full sm:w-auto px-4 sm:px-5 py-2.5 bg-green-600 text-white rounded-lg hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 transition-colors flex items-center justify-center font-medium shadow-md text-sm sm:text-base">
            <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Ajouter un espace
        </a>
        {% endif %}
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {% for space in spaces %}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-4 sm:p-5">
                    <div class="flex justify-between items-start mb-2">
                        <h2 class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">{{ space.name }}</h2>
                        {% if app.user %}
                            <button
                                data-space-id="{{ space.id }}"
                                data-favorited="{{ is_favorited(space) ? 'true' : 'false' }}"
                                class="favorite-btn {{ is_favorited(space) ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500' }} transition-colors"
                                title="{{ is_favorited(space) ? 'Retirer des favoris' : 'Ajouter aux favoris' }}">
                                <svg class="w-6 h-6 {{ is_favorited(space) ? 'fill-current' : '' }}" viewBox="0 0 24 24" stroke="currentColor" fill="{{ is_favorited(space) ? 'currentColor' : 'none' }}" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                </svg>
                            </button>
                        {% endif %}
                    </div>
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300 mb-4">{{ space.description|slice(0, 120) }}{% if space.description|length > 120 %}...{% endif %}</p>
                    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-2 sm:space-y-0">
                        <span class="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                            Hôte : {{ space.host.firstname }} {{ space.host.lastname }}
                        </span>
                        <a href="{{ path('app_space_show', {'id': space.id}) }}"
                           class=" lg:w-1/2 lg:mx-auto px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors shadow-sm text-center text-sm ">
                            Détails
                        </a>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="col-span-full text-center py-8">
                {% if searchQuery %}
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Aucun résultat trouvé</h3>
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300 mb-4">
                        Aucun espace ne correspond à votre recherche "{{ searchQuery }}".
                    </p>
                    <a href="{{ path('app_space_index') }}" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                        Voir tous les espaces
                    </a>
                {% else %}
                    <svg class="w-16 h-16 mx-auto mb-4 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Aucun espace disponible</h3>
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300">Aucun espace n'est disponible pour le moment.</p>
                {% endif %}
            </div>
        {% endfor %}
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="{{ asset('assets/js/favorites.js') }}" defer></script>
{% endblock %}
