<div class="mb-6">
    <div class="p-4 bg-white dark:bg-gray-700 rounded-lg">
        <p class="font-medium text-gray-900 dark:text-white">{{ desk.name }}</p>
        <p class="text-gray-700 dark:text-gray-300 mt-1">{{ desk.description }}</p>
        <div class="flex flex-wrap gap-2 mt-3">
            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded dark:bg-blue-900 dark:text-blue-300">
                Capacité: {{ desk.capacity }}
            </span>
            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded dark:bg-green-900 dark:text-green-300">
                {{ desk.pricePerDay }}€ / jour
            </span>
            <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs font-medium rounded dark:bg-gray-900 dark:text-gray-300">
                {% set deskTypes = constant('App\\Entity\\Desk::DESK_TYPES') %}
                {{ deskTypes[desk.type] is defined ? deskTypes[desk.type] : desk.type }}
            </span>
            {% if desk.equipments|length > 0 %}
                {% for equipment in desk.equipments %}
                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded dark:bg-yellow-900 dark:text-yellow-300">
                        {{ equipment.name }}
                    </span>
                {% endfor %}
            {% endif %}
        </div>
        <div class="mt-4">
            <div class="flex justify-end">
               {% if is_guest() and reserveButton is defined and reserveButton %}
                   <a
                           href="{{ path('app_reservation_new', {'id': desk.id}) }}"
                           class="text-white bg-green-700 hover:bg-green-800 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-4 py-2 text-center inline-flex items-center dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800"
                   >
                       <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                           <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                       </svg>
                       Reserve Now
                   </a>
               {% endif %}
            </div>
        </div>
    </div>
</div>