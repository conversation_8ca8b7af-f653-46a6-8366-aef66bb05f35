<aside
        class="fixed top-0 left-0 z-40 w-64 h-screen pt-14 transition-transform -translate-x-full bg-white border-r border-gray-200 md:translate-x-0 dark:bg-gray-800 dark:border-gray-700"
        aria-label="Sidenav"
        id="drawer-navigation"
>
    <div class="overflow-y-auto py-5 px-3 h-full bg-white dark:bg-gray-800">
        <ul class="space-y-2">
            <!-- Admin Section -->
            {% if is_admin() %}
                <li class="pt-2">
                    <div class="flex items-center px-2 py-1">
                        <div class="flex-1 h-px bg-gray-300 dark:bg-gray-600"></div>
                        <span class="px-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Administration</span>
                        <div class="flex-1 h-px bg-gray-300 dark:bg-gray-600"></div>
                    </div>
                </li>
                <li>
                    <a
                            href="{{ path('app_admin_dashboard') }}"
                            class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group {% if app.request.get('_route') == 'app_admin_dashboard' %}bg-gray-100 dark:bg-gray-700{% endif %}"
                    >
                        <svg
                                aria-hidden="true"
                                class="w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                        >
                            <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"></path>
                            <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"></path>
                        </svg>
                        <span class="ml-3">Dashboard</span>
                    </a>
                </li>
                <li>
                    <a
                            href="{{ path('app_admin_users') }}"
                            class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group {% if app.request.get('_route') == 'app_admin_users' %}bg-gray-100 dark:bg-gray-700{% endif %}"
                    >
                        <svg
                                aria-hidden="true"
                                class="w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                        >
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-3">Utilisateurs</span>
                    </a>
                </li>
                <li>
                    <a
                            href="{{ path('app_admin_spaces') }}"
                            class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group {% if app.request.get('_route') == 'app_admin_spaces' %}bg-gray-100 dark:bg-gray-700{% endif %}"
                    >
                        <svg
                                aria-hidden="true"
                                class="w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                        >
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-3">Espaces</span>
                    </a>
                </li>
                <li>
                    <a
                            href="{{ path('app_admin_reservations') }}"
                            class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group {% if app.request.get('_route') == 'app_admin_reservations' %}bg-gray-100 dark:bg-gray-700{% endif %}"
                    >
                        <svg
                                aria-hidden="true"
                                class="w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                        >
                            <path fill-rule="evenodd" d="M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v7h-2l-1 2H8l-1-2H5V5z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-3">Reservations</span>
                    </a>
                </li>
            {% endif %}

            <!-- Host Section -->
            {% if is_host() %}
                <li class="pt-4">
                    <div class="flex items-center px-2 py-1">
                        <div class="flex-1 h-px bg-gray-300 dark:bg-gray-600"></div>
                        <span class="px-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Hôte</span>
                        <div class="flex-1 h-px bg-gray-300 dark:bg-gray-600"></div>
                    </div>
                </li>
                <li>
                    <a
                            href="{{ path('app_my_spaces') }}"
                            class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group"
                            id="my-spaces-link"
                    >
                        <svg
                                aria-hidden="true"
                                class="w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                        >
                            <path fill-rule="evenodd" d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-3">Mes Espaces</span>
                    </a>
                </li>
                <li>
                    <a
                            href="{{ path('app_space_new') }}"
                            class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg  hover:bg-gray-100 dark:hover:bg-gray-700 group"
                    >
                        <svg
                                aria-hidden="true"
                                class="w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                        >
                            <path fill-rule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-3">Créer Espace</span>
                    </a>
                </li>
            {% endif %}

            <!-- Guest Section -->
            {% if is_guest() %}
                <li class="pt-4">
                    <div class="flex items-center px-2 py-1">
                        <div class="flex-1 h-px bg-gray-300 dark:bg-gray-600"></div>
                        <span class="px-3 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Invité</span>
                        <div class="flex-1 h-px bg-gray-300 dark:bg-gray-600"></div>
                    </div>
                </li>
                <li>
                    <a
                            href="{{ path('app_space_index') }}"
                            class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg  hover:bg-gray-100 dark:hover:bg-gray-700 group {% if app.request.get('_route') == 'app_space_index' %}bg-gray-100 dark:bg-gray-700{% endif %}"
                    >
                        <svg
                                aria-hidden="true"
                                class="w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                        >
                            <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-3">Espaces</span>
                    </a>
                </li>
                <li>
                    <a
                            href="{{ path('app_favorites_index') }}"
                            class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg  hover:bg-gray-100 dark:hover:bg-gray-700 group {% if app.request.get('_route') == 'app_favorites_index' %}bg-gray-100 dark:bg-gray-700{% endif %}"
                    >
                        <svg
                                aria-hidden="true"
                                class="w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                        >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <span class="ml-3">Mes Favoris</span>
                    </a>
                </li>
                <li>
                    <a
                            href="{{ path('app_reservation_index') }}"
                            class="flex items-center p-2 text-base font-medium text-gray-900 rounded-lg  hover:bg-gray-100 dark:hover:bg-gray-700 group"
                    >
                        <svg
                                aria-hidden="true"
                                class="w-6 h-6 text-gray-500 transition duration-75 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-white"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                        >
                            <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-3">Mes Reservations</span>
                    </a>
                </li>
            {% endif %}
        </ul>
    </div>
</aside>