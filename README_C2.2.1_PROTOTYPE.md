# C2.2.1 - Concevoir un Prototype de l'Application Logicielle

## 📋 Contexte du Projet

**FlexOffice** est une plateforme web de gestion et réservation d'espaces de coworking développée dans le cadre du Mastère Expert en Développement Web. Cette application répond aux besoins croissants de flexibilité dans l'organisation du travail en permettant la réservation d'espaces de travail partagés.

## 🎯 Objectifs du Prototype

Le prototype FlexOffice démontre la conception d'une application logicielle complète intégrant :
- Une architecture logicielle structurée et maintenable
- Des spécificités ergonomiques adaptées aux utilisateurs
- Un ciblage prioritaire sur les équipements web desktop
- L'implémentation des fonctionnalités métier attendues
- L'intégration des exigences de sécurité dès la conception

## 🏗️ Architecture Logicielle

### Framework et Technologies

**Stack Technique :**
- **Framework Backend** : Symfony 7.2 (PHP 8.2+)
- **Architecture** : MVC (Model-View-Controller)
- **ORM** : Doctrine ORM 3.5
- **Base de données** : MariaDB 10.11
- **Frontend** : Twig + TailwindCSS + Flowbite
- **Containerisation** : Docker & Docker Compose

**Justification des choix :**
- Symfony offre une architecture MVC robuste et des composants réutilisables
- Doctrine assure une abstraction de base de données sécurisée avec l'ORM
- TailwindCSS permet un développement rapide avec un design system cohérent
- Docker garantit la portabilité et la reproductibilité de l'environnement

### Structure du Projet

```
FlexOffice/
├── src/
│   ├── Controller/          # Contrôleurs MVC
│   ├── Entity/             # Entités métier (modèles)
│   ├── Form/               # Types de formulaires
│   ├── Repository/         # Couche d'accès aux données
│   ├── Security/           # Composants de sécurité
│   ├── Service/            # Services métier
│   └── Twig/               # Extensions Twig personnalisées
├── templates/              # Vues Twig
│   ├── components/         # Composants réutilisables
│   ├── shared/elements/    # Éléments de formulaires
│   └── [modules]/          # Templates par module métier
├── config/                 # Configuration Symfony
├── tests/                  # Tests unitaires et fonctionnels
└── assets/                 # Ressources frontend
```

### Modèle de Données

**Entités Principales :**

1. **User** - Gestion des utilisateurs avec système de rôles
   - Propriétés : email, password, firstname, lastname, roles, isVerified
   - Relations : OneToMany avec Space (host), Reservation (guest), Favorite
   - Validation : contraintes d'unicité, format email, longueur des champs

2. **Space** - Espaces de coworking
   - Propriétés : name, description
   - Relations : ManyToOne avec User (host), Address ; OneToMany avec Desk
   - Validation : nom obligatoire (3-60 caractères), description (min 10 caractères)

3. **Desk** - Bureaux individuels dans les espaces
   - Propriétés : name, type, description, pricePerDay, capacity, isAvailable
   - Relations : ManyToOne avec Space ; OneToMany avec Reservation
   - Validation : prix positif, capacité positive, nom obligatoire

4. **Reservation** - Réservations des bureaux
   - Propriétés : reservationDate, status
   - Relations : ManyToOne avec User (guest), Desk
   - Validation : date obligatoire, statut défini

5. **Address** - Adresses géographiques
   - Propriétés : street, city, postalCode, country, latitude, longitude
   - Relations : OneToMany avec Space
   - Validation : tous les champs obligatoires sauf coordonnées GPS

6. **Favorite** - Relations de favoris utilisateur-espace
   - Propriétés : createdAt
   - Relations : ManyToOne avec User, Space
   - Contrainte : unicité user-space

## 🔐 Sécurité Intégrée

### Authentification et Autorisation

**Système de Rôles :**
- `ROLE_GUEST` : Réservation d'espaces
- `ROLE_HOST` : Création et gestion d'espaces
- `ROLE_ADMIN` : Administration complète

**Mécanismes de Sécurité :**
- Authentification par formulaire avec protection CSRF
- Hachage automatique des mots de passe (bcrypt/argon2)
- Vérification d'email obligatoire
- Contrôles d'accès par route et par méthode
- Protection contre les injections SQL via l'ORM Doctrine

**Configuration Sécurité (security.yaml) :**
```yaml
access_control:
    - { path: ^/admin, roles: ROLE_ADMIN }
    - { path: ^/, roles: IS_AUTHENTICATED_FULLY }
```

### Validation des Données

**Validation côté serveur :**
- Annotations Symfony Validator sur les entités
- Validation des formulaires avec messages d'erreur en français
- Contraintes d'unicité en base de données
- Validation des types de données et formats

**Exemple de validation (Entity/User.php) :**
```php
#[Assert\NotBlank(message: 'L\'email est obligatoire')]
#[Assert\Email(message: 'L\'email doit être valide')]
private ?string $email = null;
```

## 🎨 Interface Utilisateur et Ergonomie

### Design System et Composants

**Approche Design :**
- Design responsive mobile-first avec TailwindCSS
- Composants Twig réutilisables pour la cohérence
- Thème sombre/clair adaptatif
- Interface en français pour l'accessibilité

**Composants Réutilisables :**
- Formulaires standardisés avec thème personnalisé
- Système de navigation avec sidebar responsive
- Composants de pagination
- Modales pour les actions rapides
- Sélecteurs de disponibilité interactifs

### Expérience Utilisateur

**Navigation Adaptative :**
- Redirection automatique selon le rôle utilisateur
- Sidebar contextuelle avec sections par rôle
- Breadcrumbs et liens de retour
- Recherche en temps réel

**Interactions Avancées :**
- Calendrier interactif avec Flatpickr
- Système de favoris AJAX
- Messages flash positionnés et temporisés
- Formulaires avec validation en temps réel

## 🚀 Fonctionnalités Métier Implémentées

### Gestion des Espaces
- Création d'espaces par les hosts
- Configuration des disponibilités (jours de la semaine)
- Gestion des adresses avec géolocalisation
- Upload et gestion d'images

### Système de Réservation
- Réservation de bureaux par date
- Vérification automatique des disponibilités
- Gestion des statuts de réservation
- Historique des réservations

### Administration
- Dashboard avec statistiques
- Gestion CRUD complète des utilisateurs
- Supervision des espaces et réservations
- Pagination et filtres avancés

### Fonctionnalités Transversales
- Système de favoris
- Recherche multicritères
- Notifications par email
- Export de données

## 🧪 Qualité et Tests

### Stratégie de Tests

**Couverture de Tests :**
- Tests unitaires des entités avec validation
- Tests fonctionnels des contrôleurs
- Tests d'intégration avec base de données
- Couverture de code avec PHPUnit et Codecov

**Configuration Tests :**
- Base de données SQLite en mémoire pour les tests
- Fixtures avec Doctrine Fixtures et Foundry
- Tests automatisés via GitHub Actions
- Environnement de test isolé

**Exemples de Tests :**
```php
public function testValidUserIsPersisted(): void
{
    $user = $this->makeValidUser();
    $this->em->persist($user);
    $this->em->flush();
    
    $found = $this->em->getRepository(User::class)
        ->findOneBy(['email' => '<EMAIL>']);
    
    $this->assertNotNull($found);
}
```

## 📊 Métriques et Performance

**Indicateurs Qualité :**
- Couverture de code : [![codecov](https://codecov.io/gh/Hugo-Gehringer/FlexOffice/graph/badge.svg?token=GQMX4Q9DUU)](https://codecov.io/gh/Hugo-Gehringer/FlexOffice)
- Tests automatisés sur chaque commit
- Validation continue de l'intégrité des données

**Optimisations :**
- Requêtes optimisées avec jointures Doctrine
- Pagination pour les grandes listes
- Cache Symfony pour les performances
- Assets compilés et minifiés

## 🔄 Évolutivité et Maintenabilité

### Architecture Modulaire
- Séparation claire des responsabilités (MVC)
- Services métier réutilisables
- Composants Twig modulaires
- Configuration externalisée

### Bonnes Pratiques
- Respect des standards PSR (PHP Standards Recommendations)
- Documentation du code et des API
- Gestion des erreurs centralisée
- Logs structurés pour le debugging

### Extensibilité
- Système de rôles extensible
- Entités avec relations flexibles
- Hooks et événements Symfony
- API REST potentielle pour intégrations futures

## 🎯 Conclusion

Le prototype FlexOffice démontre une maîtrise complète de la conception d'applications web modernes en intégrant :

✅ **Architecture solide** : Framework Symfony avec pattern MVC et ORM Doctrine  
✅ **Sécurité native** : Authentification, autorisation, validation et protection CSRF  
✅ **UX/UI optimisée** : Interface responsive, composants réutilisables, interactions fluides  
✅ **Qualité assurée** : Tests automatisés, couverture de code, intégration continue  
✅ **Maintenabilité** : Code structuré, documentation, bonnes pratiques  

Cette approche garantit un prototype fonctionnel, sécurisé et évolutif, répondant aux exigences professionnelles du développement d'applications logicielles.
