name: Symfony

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main", "dev" ]

permissions:
  contents: read

jobs:
  symfony-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: shivammathur/setup-php@2cb9b829437ee246e9b3cac53555a39208ca6d28
        with:
          php-version: '8.2'
          coverage: xdebug   # <--- ACTIVE XDEBUG pour coverage

      - uses: actions/checkout@v4

      - name: Copy .env.test.local
        run: php -r "file_exists('.env.test.local') || copy('.env.test', '.env.test.local');"

      - name: Cache Composer packages
        id: composer-cache
        uses: actions/cache@v3
        with:
          path: vendor
          key: ${{ runner.os }}-php-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            ${{ runner.os }}-php-

      - name: Install Dependencies
        run: composer install -q --no-ansi --no-interaction --no-scripts --no-progress --prefer-dist

      - name: Install NPM Dependencies
        run: npm install

      - name: build importmap
        run: php bin/console importmap:install

      - name: Build Tailwind CSS
        run: php bin/console tailwind:build

      - name: Compile Assets
        run: php bin/console asset-map:compile

      - name: Create Database
        run: |
          mkdir -p data
          touch data/database.sqlite

      - name: Run PHPUnit with coverage
        env:
          DATABASE_URL: sqlite:///%kernel.project_dir%/data/database.sqlite
          XDEBUG_MODE: coverage
          SYMFONY_DEPRECATIONS_HELPER: "weak"
        run: vendor/bin/phpunit --coverage-clover=clover.xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          files: clover.xml
          fail_ci_if_error: true
          token: ${{ secrets.CODECOV_TOKEN }}
