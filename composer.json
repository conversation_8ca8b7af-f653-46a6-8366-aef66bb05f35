{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "doctrine/dbal": "^3.10.1", "doctrine/doctrine-bundle": "^2.15.1", "doctrine/doctrine-migrations-bundle": "^3.4.2", "doctrine/orm": "^3.5.1", "knplabs/knp-paginator-bundle": "^6.9.1", "php-flasher/flasher-symfony": "^2.1.6", "phpdocumentor/reflection-docblock": "^5.6.2", "phpstan/phpdoc-parser": "^2.2", "symfony/asset": "7.2.*", "symfony/asset-mapper": "7.2.*", "symfony/console": "7.2.*", "symfony/doctrine-messenger": "7.2.*", "symfony/dotenv": "7.2.*", "symfony/expression-language": "7.2.*", "symfony/flex": "^2.8.1", "symfony/form": "7.2.*", "symfony/framework-bundle": "7.2.*", "symfony/http-client": "7.2.*", "symfony/intl": "7.2.*", "symfony/mailer": "7.2.*", "symfony/mime": "7.2.*", "symfony/monolog-bundle": "^3.10", "symfony/notifier": "7.2.*", "symfony/process": "7.2.*", "symfony/property-access": "7.2.*", "symfony/property-info": "7.2.*", "symfony/runtime": "7.2.*", "symfony/security-bundle": "7.2.*", "symfony/security-csrf": "7.2.*", "symfony/serializer": "7.2.*", "symfony/stimulus-bundle": "^2.28.2", "symfony/string": "7.2.*", "symfony/translation": "7.2.*", "symfony/twig-bundle": "7.2.*", "symfony/validator": "7.2.*", "symfony/web-link": "7.2.*", "symfony/yaml": "7.2.*", "symfonycasts/tailwind-bundle": "^0.7.1", "symfonycasts/verify-email-bundle": "^1.17.3", "tales-from-a-dev/flowbite-bundle": "^0.7.1", "twig/extra-bundle": "^2.12|^3.21", "twig/intl-extra": "^3.21", "twig/twig": "^2.12|^3.21.1"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd", "importmap:install": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.2.*"}}, "require-dev": {"doctrine/doctrine-fixtures-bundle": ">=4.1", "phpunit/phpunit": "^9.6.23", "symfony/browser-kit": "7.2.*", "symfony/css-selector": "7.2.*", "symfony/debug-bundle": "7.2.*", "symfony/maker-bundle": "^1.64", "symfony/phpunit-bridge": "^7.3.1", "symfony/stopwatch": "7.2.*", "symfony/web-profiler-bundle": "7.2.*", "zenstruck/foundry": ">=2.6.1"}}