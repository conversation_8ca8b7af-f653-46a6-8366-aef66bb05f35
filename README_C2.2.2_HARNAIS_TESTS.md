# C2.2.2 - Développer un Harnais de Test Unitaire

## 📋 Contexte du Projet

Dans le cadre du développement de **FlexOffice**, une plateforme de gestion et réservation d'espaces de coworking, la mise en place d'un harnais de test unitaire robuste est essentielle pour garantir la qualité du code et prévenir les régressions lors des évolutions fonctionnelles.

## 🎯 Objectifs du Harnais de Tests

Le harnais de tests développé vise à :
- **Automatiser la validation** des fonctionnalités critiques
- **Prévenir les régressions** lors des modifications de code
- **Garantir la fiabilité** des composants métier
- **Faciliter la maintenance** et l'évolution du projet
- **Assurer la couverture** des cas d'usage principaux

## 🏗️ Architecture Technique du Harnais

### Stack Technique

**Outils de Test :**
- **Framework** : PHPUnit 9.6.23
- **Environnement** : PHP 8.2 avec Symfony 7.2
- **Base de données** : SQLite en mémoire pour l'isolation
- **Couverture** : Xdebug avec génération de rapports
- **CI/CD** : GitHub Actions avec intégration Codecov

**Justification des choix :**
- PHPUnit est le standard de facto pour les tests PHP
- SQLite en mémoire garantit des tests rapides et isolés
- Xdebug permet une mesure précise de la couverture de code
- GitHub Actions assure l'exécution automatisée à chaque commit

### Structure du Répertoire de Tests

```
tests/
├── Controller/              # Tests fonctionnels des contrôleurs
│   ├── HomepageControllerTest.php
│   ├── SpaceControllerTest.php
│   └── UserControllerTest.php
├── Entity/                  # Tests unitaires des entités
│   ├── UserTest.php
│   ├── SpaceTest.php
│   ├── DeskTest.php
│   └── EquipmentTest.php
├── Service/                 # Tests des services métier
│   └── AvailabilityCheckerTest.php
├── Repository/              # Tests des repositories
│   └── ReservationRepositoryTest.php
├── bootstrap.php            # Configuration des tests
└── DatabaseTestCase.php     # Classe de base pour tests DB
```

## 🧪 Configuration du Harnais

### Configuration PHPUnit (phpunit.xml.dist)

```xml
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         bootstrap="tests/bootstrap.php"
         colors="true">
    <php>
        <server name="APP_ENV" value="test" force="true"/>
        <env name="KERNEL_CLASS" value="App\Kernel"/>
    </php>
    
    <testsuites>
        <testsuite name="Project Test Suite">
            <directory>tests</directory>
        </testsuite>
    </testsuites>
    
    <coverage processUncoveredFiles="true">
        <include>
            <directory suffix=".php">src</directory>
        </include>
        <exclude>
            <directory suffix=".php">src/DataFixtures</directory>
        </exclude>
        <report>
            <clover outputFile="clover.xml"/>
            <html outputDirectory="coverage-html"/>
        </report>
    </coverage>
</phpunit>
```

### Bootstrap des Tests (tests/bootstrap.php)

```php
<?php
use Symfony\Component\Dotenv\Dotenv;

require dirname(__DIR__).'/vendor/autoload.php';

// Charge les variables d'environnement de test
if (file_exists(dirname(__DIR__) . '/.env.test')) {
    (new Dotenv())->usePutenv()->bootEnv(dirname(__DIR__) . '/.env.test');
}

// Configuration base de données SQLite en mémoire
if (isset($_ENV['DATABASE_URL']) && $_ENV['DATABASE_URL'] === 'sqlite:///:memory:') {
    $kernel = new \App\Kernel('test', true);
    $kernel->boot();
    
    $entityManager = $kernel->getContainer()->get('doctrine.orm.entity_manager');
    $schemaTool = new \Doctrine\ORM\Tools\SchemaTool($entityManager);
    $metadata = $entityManager->getMetadataFactory()->getAllMetadata();
    
    if (!empty($metadata)) {
        $schemaTool->createSchema($metadata);
    }
}
```

## 🎯 Fonctionnalité Testée : Système de Réservation

### Description du Comportement Métier

La fonctionnalité critique testée est le **système de vérification de disponibilité des bureaux** qui doit :

1. **Vérifier la disponibilité d'un bureau** à une date donnée
2. **Respecter les horaires d'ouverture** de l'espace
3. **Éviter les conflits de réservation** (double-booking)
4. **Valider les jours d'ouverture** configurés par l'hôte

### Service Testé : AvailabilityChecker

<augment_code_snippet path="src/Service/AvailabilityChecker.php" mode="EXCERPT">
````php
class AvailabilityChecker
{
    public function __construct(
        private ReservationRepository $reservationRepository
    ) {}

    public function isDeskAvailableOnDate(Desk $desk, \DateTime $date): bool
    {
        // Vérifier si le bureau est marqué comme disponible
        if (!$desk->isAvailable()) {
            return false;
        }

        // Vérifier la disponibilité de l'espace parent
        $availability = $desk->getSpace()->getAvailability();
        if (!$availability || !$this->isDateInAvailableDay($date, $availability)) {
            return false;
        }

        // Vérifier qu'il n'y a pas de réservation existante
        return $this->reservationRepository->isDeskAvailableOnDate($desk, $date);
    }
}
````
</augment_code_snippet>

## 🧪 Exemples de Tests Unitaires

### Test de Validation d'Entité

<augment_code_snippet path="tests/Entity/UserTest.php" mode="EXCERPT">
````php
class UserTest extends KernelTestCase
{
    private EntityManagerInterface $em;
    private ValidatorInterface $validator;

    public function testValidUserIsPersisted(): void
    {
        $user = $this->makeValidUser();
        
        $this->em->persist($user);
        $this->em->flush();
        
        $found = $this->em->getRepository(User::class)
            ->findOneBy(['email' => '<EMAIL>']);
        
        $this->assertNotNull($found);
        $this->assertEquals('<EMAIL>', $found->getEmail());
    }

    public function testInvalidEmailIsRejected(): void
    {
        $user = $this->makeValidUser();
        $user->setEmail('invalid-email');
        
        $violations = $this->validator->validate($user);
        
        $this->assertCount(1, $violations);
        $this->assertEquals('L\'email doit être valide', $violations[0]->getMessage());
    }
}
````
</augment_code_snippet>

### Test de Service Métier avec Mocks

<augment_code_snippet path="tests/Service/AvailabilityCheckerTest.php" mode="EXCERPT">
````php
class AvailabilityCheckerTest extends TestCase
{
    private AvailabilityChecker $availabilityChecker;
    private ReservationRepository|MockObject $reservationRepository;

    protected function setUp(): void
    {
        $this->reservationRepository = $this->createMock(ReservationRepository::class);
        $this->availabilityChecker = new AvailabilityChecker($this->reservationRepository);
    }

    public function testIsDeskAvailableOnDateWhenAllConditionsAreMet(): void
    {
        $desk = $this->createTestDesk(true);
        $date = new \DateTime('2025-07-01'); // Tuesday (available day)

        // Mock repository to return true (no reservations)
        $this->reservationRepository
            ->expects($this->once())
            ->method('isDeskAvailableOnDate')
            ->with($desk, $date)
            ->willReturn(true);

        $result = $this->availabilityChecker->isDeskAvailableOnDate($desk, $date);

        $this->assertTrue($result);
    }

    public function testIsDeskAvailableOnDateWhenDeskIsNotAvailable(): void
    {
        $desk = $this->createTestDesk(false); // Desk marked as not available
        $date = new \DateTime('2025-07-01');

        $result = $this->availabilityChecker->isDeskAvailableOnDate($desk, $date);

        $this->assertFalse($result);
    }
}
````
</augment_code_snippet>

### Test de Repository avec Base de Données

<augment_code_snippet path="tests/Repository/ReservationRepositoryTest.php" mode="EXCERPT">
````php
class ReservationRepositoryTest extends DatabaseTestCase
{
    public function testIsDeskAvailableOnDateWhenAvailable(): void
    {
        $testDate = new DateTime('2024-01-15');
        
        $isAvailable = $this->repository->isDeskAvailableOnDate($this->testDesk, $testDate);
        
        $this->assertTrue($isAvailable);
    }

    public function testIsDeskAvailableOnDateWhenReserved(): void
    {
        $testDate = new DateTime('2024-01-15');
        
        // Créer une réservation confirmée
        $this->createReservation($this->testDesk, $testDate, Reservation::STATUS_CONFIRMED);
        $this->em->flush();
        
        $isAvailable = $this->repository->isDeskAvailableOnDate($this->testDesk, $testDate);
        
        $this->assertFalse($isAvailable);
    }
}
````
</augment_code_snippet>

## 🚀 Exécution des Tests

### Exécution Manuelle en Local

```bash
# Exécution complète de la suite de tests
vendor/bin/phpunit

# Exécution avec couverture de code
vendor/bin/phpunit --coverage-html=coverage

# Exécution d'une classe spécifique
vendor/bin/phpunit tests/Service/AvailabilityCheckerTest.php

# Exécution avec Makefile
make test-coverage
```

### Automatisation via GitHub Actions

<augment_code_snippet path=".github/workflows/symfony.yml" mode="EXCERPT">
````yaml
name: Symfony

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  symfony-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          coverage: xdebug

      - uses: actions/checkout@v4

      - name: Install Dependencies
        run: composer install --no-interaction --prefer-dist

      - name: Run PHPUnit with coverage
        env:
          DATABASE_URL: sqlite:///%kernel.project_dir%/data/database.sqlite
          XDEBUG_MODE: coverage
        run: vendor/bin/phpunit --coverage-clover=clover.xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          files: clover.xml
          token: ${{ secrets.CODECOV_TOKEN }}
````
</augment_code_snippet>

## 📊 Mesure de la Qualité

### Métriques de Couverture

**Couverture Actuelle :**
[![codecov](https://codecov.io/gh/Hugo-Gehringer/FlexOffice/graph/badge.svg?token=GQMX4Q9DUU)](https://codecov.io/gh/Hugo-Gehringer/FlexOffice)

**Objectifs de Couverture :**
- **Global** : Minimum 70% de couverture de code
- **Services critiques** : Minimum 90% (AvailabilityChecker, ReservationRepository)
- **Entités métier** : Minimum 80% (User, Space, Desk, Reservation)

### Rapports Générés

1. **Rapport Clover XML** : `clover.xml` pour intégration CI/CD
2. **Rapport HTML** : `coverage-html/` pour analyse détaillée
3. **Métriques Codecov** : Suivi de l'évolution de la couverture

## 🎯 Impact sur la Qualité Logicielle

### Prévention des Régressions

**Cas de régression détectés :**
- Modification de la logique de disponibilité sans mise à jour des tests
- Changement de validation d'entité cassant les contraintes métier
- Refactoring de repository modifiant le comportement des requêtes

### Gain de Confiance

**Bénéfices observés :**
- **Déploiements sécurisés** : Validation automatique avant mise en production
- **Refactoring facilité** : Modification du code avec assurance de non-régression
- **Documentation vivante** : Les tests servent de spécification du comportement

### Amélioration de la Robustesse

**Qualité du code renforcée :**
- **Validation des cas limites** : Tests des edge cases et erreurs
- **Isolation des composants** : Tests unitaires avec mocks pour l'indépendance
- **Intégration continue** : Validation automatique à chaque modification

## 🔄 Évolution et Maintenance

### Stratégie de Tests

**Approche adoptée :**
- **Test-Driven Development** pour les nouvelles fonctionnalités
- **Tests de régression** pour les bugs corrigés
- **Tests d'intégration** pour les workflows complets

### Maintenance du Harnais

**Bonnes pratiques :**
- Mise à jour des tests lors des évolutions fonctionnelles
- Refactoring des tests en parallèle du code de production
- Surveillance continue des métriques de couverture

## 🎯 Conclusion

Le harnais de test unitaire développé pour FlexOffice démontre une approche professionnelle de la qualité logicielle :

✅ **Couverture complète** : Tests unitaires, fonctionnels et d'intégration  
✅ **Automatisation** : Exécution continue via GitHub Actions  
✅ **Métriques** : Suivi de la couverture avec Codecov  
✅ **Isolation** : Tests indépendants avec base SQLite en mémoire  
✅ **Robustesse** : Validation des cas nominaux et d'erreur  

Cette approche garantit la fiabilité du système de réservation et facilite l'évolution future de la plateforme en prévenant efficacement les régressions.
